name: doc_test

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - v0.*
  pull_request:
    branches:
      - main
      - v0.*
    paths:
      - "**/*.py"
      - "docs/**"
      - .github/workflows/doc.yml

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# Declare permissions just read content.
permissions:
  contents: read      # for checkout
  pages: write        # for deploy-pages
  id-token: write     # for deploy-pages

jobs:
  doc_test:
    runs-on: ubuntu-latest
    timeout-minutes: 5 # Increase this timeout value as needed
    strategy:
      matrix:
        python-version: ["3.10"]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@0b93645e9fea7318ecaed2b359559ac225c90a2b # v5.3.0
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install the current repository
        run: |
          pip install -e .[test]
          pip install -r docs/requirements-docs.txt

      - name: Run doc make html
        run: |
          cd docs 
          make clean
          make html SPHINXOPTS="--keep-going -w _build/sphinx.log"
          if grep -q ": ERROR:" _build/sphinx.log; then
            echo "🚨 Sphinx doc build contained ERRORs - see _build/sphinx.log"
            exit 1
          fi
