name: sgl

on:
  workflow_dispatch: # Manual
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - v0.2.x
    paths:
      - "**/*.py"
      - .github/workflows/vllm.yml
  pull_request:
    branches:
      - main
      - v0.2.x
    paths:
      - "**/*.py"
      # Other entrypoints
      - "!examples/**"
      - "!tests/**"
      - "!verl/trainer/main_*.py"
      - "!verl/trainer/fsdp_sft_trainer.py"
      # FSDP
      - "!verl/workers/**/*dp_*.py"
      # Megatron
      - "!verl/workers/**/megatron_*.py"
      # vLLM
      - "!**/*vllm*"
      # Recipes
      - "!recipe/**"
      # Entrypoints
      - ".github/workflows/sgl.yml"
      - "tests/rollout/*sglang*"
      - "tests/rollout/async_rollout_utils.py"

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# Declare permissions just read content.
permissions:
  contents: read

jobs:
  sgl:
    runs-on: [L20x8]
    timeout-minutes: 20 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1,hf-mirror.com"
      HF_ENDPOINT: "https://hf-mirror.com"
      HF_HUB_ENABLE_HF_TRANSFER: 1
      SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK: "True"
    container:
      image: whatcanyousee/verl:ngc-cu124-vllm0.8.5-sglang0.4.6.post5-mcore0.12.0-te2.3
      options: --gpus all --shm-size=10g
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Install the current repository
        run: |
          pip3 install hf_transfer
          pip3 install -e .[test,gpu,sglang] --no-deps
      - name: Download Model to Use
        run: |
          huggingface-cli download 'Qwen/Qwen2-7B-Instruct'
          export HF_HUB_OFFLINE=1
      - name: Test the latest SGLang
        run: |
          cd tests/workers/rollout
          torchrun --nnodes=1 --nproc_per_node=2 $(which pytest) -s test_sglang_spmd.py
      - name: Test the latest SGLang Rollout async with tool
        run: |
          cd tests/workers/rollout
          torchrun --nnodes=1 --nproc_per_node=2 $(which pytest) -s test_sglang_async_rollout_w_tools.py
      - name: Test the latest SGLang Rollout async with sandbox fusion tool
        run: |
          cd tests/workers/rollout
          pytest -s test_sglang_async_rollout_sf_tools.py
      - name: Test the latest SGLang Rollout async with search tool
        run: |
          cd tests/workers/rollout
          pytest -s test_sglang_async_rollout_search_tools.py