# Copyright 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from abc import ABC, abstractmethod

from verl import DataProto

__all__ = ["BaseRollout"]


class BaseRollout(ABC):
    def __init__(self):
        """

        Args:
            dataloader: an Iterable of TensorDict that consistently generates prompts. Note that the dataloader
            should handle when the training stops.
        """
        super().__init__()

    @abstractmethod
    def generate_sequences(self, prompts: DataProto) -> DataProto:
        """Generate sequences"""
        pass
