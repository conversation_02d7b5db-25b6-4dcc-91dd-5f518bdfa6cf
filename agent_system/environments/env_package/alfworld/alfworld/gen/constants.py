from collections import OrderedDict
import os

########################################################################################################################
# General Settings

DEBUG = True
EVAL = False
LOG_FILE = 'logs_gen'

RECORD_VIDEO_IMAGES = True
RECORD_SMOOTHING_FACTOR = 1
DATA_SAVE_PATH = "dataset/new_trajectories"

OPEN_LOOP = True
FULL_OBSERVABLE_STATE = True

########################################################################################################################
# Generation Ablations

MAX_NUM_OF_OBJ_INSTANCES = 3     # when randomly initializing the scene, create duplicate instance up to this number
PICKUP_REPEAT_MAX = 4            # how many of the target pickup object to generate in [1, MAX] (randomly chosen)
RECEPTACLE_SPARSE_POINTS = 50    # increment for how many points to leave free for sparsely populated receptacles
RECEPTACLE_EMPTY_POINTS = 200    # increment for how many points to leave free for empty receptacles

MIN_VISIBLE_RATIO = 0.0011       # minimum area ratio (with respect to image size) of visible object
PLANNER_MAX_STEPS = 100          # if the generated plan is more than these steps, discard the traj
MAX_EPISODE_LENGTH = 1000        # maximum number of API steps allowed per trajectory

FORCED_SAMPLING = False          # set True for debugging instead of proper sampling
PRUNE_UNREACHABLE_POINTS = False  # prune navigation points that were deemed unreachable by the proprocessing script

########################################################################################################################
# Goals

GOALS = ["pick_and_place_simple",
         "pick_two_obj_and_place",
         "look_at_obj_in_light",
         "pick_clean_then_place_in_recep",
         "pick_heat_then_place_in_recep",
         "pick_cool_then_place_in_recep",
         "pick_and_place_with_movable_recep"]

GOALS_VALID = {"pick_and_place_simple": {"Kitchen", "LivingRoom", "Bathroom", "Bedroom"},
               "pick_two_obj_and_place": {"Kitchen", "LivingRoom", "Bathroom", "Bedroom"},
               "look_at_obj_in_light": {"LivingRoom", "Bedroom"},
               "pick_clean_then_place_in_recep": {"Kitchen", "Bathroom"},
               "pick_heat_then_place_in_recep": {"Kitchen"},
               "pick_cool_then_place_in_recep": {"Kitchen"},
               "pick_and_place_with_movable_recep": {"Kitchen", "LivingRoom", "Bedroom"}}

pddl_goal_type = "pick_and_place_simple"  # default goal type

########################################################################################################################
# Video Settings

# filler frame IDs
BEFORE = 0
MIDDLE = 1
AFTER = 2

# number of image frames to save before and after executing the specified action
SAVE_FRAME_BEFORE_AND_AFTER_COUNTS = {
    'OpenObject': [2, 0, 2],
    'CloseObject': [2, 0, 2],
    'PickupObject': [5, 0, 10],
    'PutObject': [5, 0, 10],
    'CleanObject': [3, 0, 5],
    'HeatObject': [3, 0, 5],
    'CoolObject': [3, 30, 5],
    'ToggleObjectOn': [3, 0, 15],
    'ToggleObjectOff': [1, 0, 5],
    'SliceObject': [3, 0, 7]
}

# FPS
VIDEO_FRAME_RATE = 5

########################################################################################################################
# Data & Storage

save_path = DATA_SAVE_PATH
data_dict = OrderedDict()  # dictionary for storing trajectory data to be dumped

########################################################################################################################
# Unity Hyperparameters

BUILD_PATH = None
X_DISPLAY = None

AGENT_STEP_SIZE = 0.25
AGENT_HORIZON_ADJ = 15
AGENT_ROTATE_ADJ = 90
CAMERA_HEIGHT_OFFSET = 0.75
VISIBILITY_DISTANCE = 1.5
HORIZON_GRANULARITY = 15

RENDER_IMAGE = True
RENDER_DEPTH_IMAGE = True
RENDER_CLASS_IMAGE = True
RENDER_OBJECT_IMAGE = True

MAX_DEPTH = 5000
STEPS_AHEAD = 5
SCENE_PADDING = STEPS_AHEAD * 3
SCREEN_WIDTH = DETECTION_SCREEN_WIDTH = 300
SCREEN_HEIGHT = DETECTION_SCREEN_HEIGHT = 300
MIN_VISIBLE_PIXELS = 10

# (400) / (600*600) ~ 0.13% area of image
# int(MIN_VISIBLE_RATIO * float(DETECTION_SCREEN_WIDTH) * float(DETECTION_SCREEN_HEIGHT))
# MIN_VISIBLE_PIXELS = int(MIN_VISIBLE_RATIO * float(DETECTION_SCREEN_WIDTH) * float(
#    DETECTION_SCREEN_HEIGHT))  # (400) / (600*600) ~ 0.13% area of image

########################################################################################################################
# Scenes and Objects

TRAIN_SCENE_NUMBERS = list(range(7, 31))           # Train Kitchens (24/30)
TRAIN_SCENE_NUMBERS.extend(list(range(207, 231)))  # Train Living Rooms (24/30)
TRAIN_SCENE_NUMBERS.extend(list(range(307, 331)))  # Train Bedrooms (24/30)
TRAIN_SCENE_NUMBERS.extend(list(range(407, 431)))  # Train Bathrooms (24/30)

TEST_SCENE_NUMBERS = list(range(1, 7))             # Test Kitchens (6/30)
TEST_SCENE_NUMBERS.extend(list(range(201, 207)))   # Test Living Rooms (6/30)
TEST_SCENE_NUMBERS.extend(list(range(301, 307)))   # Test Bedrooms (6/30)
TEST_SCENE_NUMBERS.extend(list(range(401, 407)))   # Test Bathrooms (6/30)

SCENE_NUMBERS = TRAIN_SCENE_NUMBERS + TEST_SCENE_NUMBERS

# Scene types.
SCENE_TYPE = {"Kitchen": range(1, 31),
              "LivingRoom": range(201, 231),
              "Bedroom": range(301, 331),
              "Bathroom": range(401, 431)}

OBJECTS = [
    'AlarmClock',
    'Apple',
    'ArmChair',
    'BaseballBat',
    'BasketBall',
    'Bathtub',
    'BathtubBasin',
    'Bed',
    'Blinds',
    'Book',
    'Boots',
    'Bowl',
    'Box',
    'Bread',
    'ButterKnife',
    'Cabinet',
    'Candle',
    'Cart',
    'CD',
    'CellPhone',
    'Chair',
    'Cloth',
    'CoffeeMachine',
    'CounterTop',
    'CreditCard',
    'Cup',
    'Curtains',
    'Desk',
    'DeskLamp',
    'DishSponge',
    'Drawer',
    'Dresser',
    'Egg',
    'FloorLamp',
    'Footstool',
    'Fork',
    'Fridge',
    'GarbageCan',
    'Glassbottle',
    'HandTowel',
    'HandTowelHolder',
    'HousePlant',
    'Kettle',
    'KeyChain',
    'Knife',
    'Ladle',
    'Laptop',
    'LaundryHamper',
    'LaundryHamperLid',
    'Lettuce',
    'LightSwitch',
    'Microwave',
    'Mirror',
    'Mug',
    'Newspaper',
    'Ottoman',
    'Painting',
    'Pan',
    'PaperTowel',
    'PaperTowelRoll',
    'Pen',
    'Pencil',
    'PepperShaker',
    'Pillow',
    'Plate',
    'Plunger',
    'Poster',
    'Pot',
    'Potato',
    'RemoteControl',
    'Safe',
    'SaltShaker',
    'ScrubBrush',
    'Shelf',
    'ShowerDoor',
    'ShowerGlass',
    'Sink',
    'SinkBasin',
    'SoapBar',
    'SoapBottle',
    'Sofa',
    'Spatula',
    'Spoon',
    'SprayBottle',
    'Statue',
    'StoveBurner',
    'StoveKnob',
    'DiningTable',
    'CoffeeTable',
    'SideTable',
    'TeddyBear',
    'Television',
    'TennisRacket',
    'TissueBox',
    'Toaster',
    'Toilet',
    'ToiletPaper',
    'ToiletPaperHanger',
    'ToiletPaperRoll',
    'Tomato',
    'Towel',
    'TowelHolder',
    'TVStand',
    'Vase',
    'Watch',
    'WateringCan',
    'Window',
    'WineBottle',
]

OBJECTS_WSLICED = sorted(OBJECTS +
                         ['AppleSliced', 'BreadSliced', 'LettuceSliced', 'PotatoSliced', 'TomatoSliced'] +
                         ['Faucet'])

OBJECTS_LOWER_TO_UPPER = {obj.lower(): obj for obj in OBJECTS}

OBJECTS_SINGULAR = [
    'alarmclock',
    'apple',
    'armchair',
    'baseballbat',
    'basketball',
    'bathtub',
    'bathtubbasin',
    'bed',
    'blinds',
    'book',
    'boots',
    'bowl',
    'box',
    'bread',
    'butterknife',
    'cabinet',
    'candle',
    'cart',
    'cd',
    'cellphone',
    'chair',
    'cloth',
    'coffeemachine',
    'countertop',
    'creditcard',
    'cup',
    'curtains',
    'desk',
    'desklamp',
    'dishsponge',
    'drawer',
    'dresser',
    'egg',
    'floorlamp',
    'footstool',
    'fork',
    'fridge',
    'garbagecan',
    'glassbottle',
    'handtowel',
    'handtowelholder',
    'houseplant',
    'kettle',
    'keychain',
    'knife',
    'ladle',
    'laptop',
    'laundryhamper',
    'laundryhamperlid',
    'lettuce',
    'lightswitch',
    'microwave',
    'mirror',
    'mug',
    'newspaper',
    'ottoman',
    'painting',
    'pan',
    'papertowel',
    'papertowelroll',
    'pen',
    'pencil',
    'peppershaker',
    'pillow',
    'plate',
    'plunger',
    'poster',
    'pot',
    'potato',
    'remotecontrol',
    'safe',
    'saltshaker',
    'scrubbrush',
    'shelf',
    'showerdoor',
    'showerglass',
    'sink',
    'sinkbasin',
    'soapbar',
    'soapbottle',
    'sofa',
    'spatula',
    'spoon',
    'spraybottle',
    'statue',
    'stoveburner',
    'stoveknob',
    'diningtable',
    'coffeetable',
    'sidetable'
    'teddybear',
    'television',
    'tennisracket',
    'tissuebox',
    'toaster',
    'toilet',
    'toiletpaper',
    'toiletpaperhanger',
    'toiletpaperroll',
    'tomato',
    'towel',
    'towelholder',
    'tvstand',
    'vase',
    'watch',
    'wateringcan',
    'window',
    'winebottle',
]

OBJECTS_PLURAL = [
    'alarmclocks',
    'apples',
    'armchairs',
    'baseballbats',
    'basketballs',
    'bathtubs',
    'bathtubbasins',
    'beds',
    'blinds',
    'books',
    'boots',
    'bottles',
    'bowls',
    'boxes',
    'bread',
    'butterknives',
    'cabinets',
    'candles',
    'carts',
    'cds',
    'cellphones',
    'chairs',
    'cloths',
    'coffeemachines',
    'countertops',
    'creditcards',
    'cups',
    'curtains',
    'desks',
    'desklamps',
    'dishsponges',
    'drawers',
    'dressers',
    'eggs',
    'floorlamps',
    'footstools',
    'forks',
    'fridges',
    'garbagecans',
    'glassbottles',
    'handtowels',
    'handtowelholders',
    'houseplants',
    'kettles',
    'keychains',
    'knives',
    'ladles',
    'laptops',
    'laundryhampers',
    'laundryhamperlids',
    'lettuces',
    'lightswitches',
    'microwaves',
    'mirrors',
    'mugs',
    'newspapers',
    'ottomans',
    'paintings',
    'pans',
    'papertowels',
    'papertowelrolls',
    'pens',
    'pencils',
    'peppershakers',
    'pillows',
    'plates',
    'plungers',
    'posters',
    'pots',
    'potatoes',
    'remotecontrollers',
    'safes',
    'saltshakers',
    'scrubbrushes',
    'shelves',
    'showerdoors',
    'showerglassess',
    'sinks',
    'sinkbasins',
    'soapbars',
    'soapbottles',
    'sofas',
    'spatulas',
    'spoons',
    'spraybottles',
    'statues',
    'stoveburners',
    'stoveknobs',
    'diningtables',
    'coffeetables',
    'sidetable',
    'teddybears',
    'televisions',
    'tennisrackets',
    'tissueboxes',
    'toasters',
    'toilets',
    'toiletpapers',
    'toiletpaperhangers',
    'toiletpaperrolls',
    'tomatoes',
    'towels',
    'towelholders',
    'tvstands',
    'vases',
    'watches',
    'wateringcans',
    'windows',
    'winebottles',
]

MOVABLE_RECEPTACLES = [
    'Bowl',
    'Box',
    'Cup',
    'Mug',
    'Plate',
    'Pan',
    'Pot',
]

MOVABLE_RECEPTACLES_SET = set(MOVABLE_RECEPTACLES)
OBJECTS_SET = set(OBJECTS) | MOVABLE_RECEPTACLES_SET

OBJECT_CLASS_TO_ID = {obj: ii for (ii, obj) in enumerate(OBJECTS)}

RECEPTACLES = {
        'BathtubBasin',
        'Bowl',
        'Cup',
        'Drawer',
        'Mug',
        'Plate',
        'Shelf',
        'SinkBasin',
        'Box',
        'Cabinet',
        'CoffeeMachine',
        'CounterTop',
        'Fridge',
        'GarbageCan',
        'HandTowelHolder',
        'Microwave',
        'PaintingHanger',
        'Pan',
        'Pot',
        'StoveBurner',
        'DiningTable',
        'CoffeeTable',
        'SideTable',
        'ToiletPaperHanger',
        'TowelHolder',
        'Safe',
        'BathtubBasin',
        'ArmChair',
        'Toilet',
        'Sofa',
        'Ottoman',
        'Dresser',
        'LaundryHamper',
        'Desk',
        'Bed',
        'Cart',
        'TVStand',
        'Toaster',
    }

NON_RECEPTACLES = OBJECTS_SET - RECEPTACLES

NUM_RECEPTACLES = len(RECEPTACLES)
NUM_CLASSES = len(OBJECTS)

# For generating questions
QUESTION_OBJECT_CLASS_LIST = [
    'Spoon',
    'Potato',
    'Fork',
    'Plate',
    'Egg',
    'Tomato',
    'Bowl',
    'Lettuce',
    'Apple',
    'Knife',
    'Container',
    'Bread',
    'Mug',
]

VAL_RECEPTACLE_OBJECTS = {
    'Pot': {'Apple',
            'AppleSliced',
            'ButterKnife',
            'DishSponge',
            'Egg',
            'Fork',
            'Knife',
            'Ladle',
            'Lettuce',
            'LettuceSliced',
            'Potato',
            'PotatoSliced',
            'Spatula',
            'Spoon',
            'Tomato',
            'TomatoSliced'},
    'Pan': {'Apple',
            'AppleSliced',
            'ButterKnife',
            'DishSponge',
            'Egg',
            'Fork',
            'Knife',
            'Ladle',
            'Lettuce',
            'LettuceSliced',
            'Potato',
            'PotatoSliced',
            'Spatula',
            'Spoon',
            'Tomato',
            'TomatoSliced'},
    'Bowl': {'Apple',
             'AppleSliced',
             'ButterKnife',
             'DishSponge',
             'Egg',
             'Fork',
             'Knife',
             'Ladle',
             'Lettuce',
             'LettuceSliced',
             'Potato',
             'PotatoSliced',
             'Spatula',
             'Spoon',
             'Tomato',
             'TomatoSliced',
             'Candle',
             'CD',
             'CellPhone',
             'Cloth',
             'CreditCard',
             'DishSponge',
             'KeyChain',
             'Mug',
             'PaperTowel',
             'Pen',
             'Pencil',
             'RemoteControl',
             'Watch'},
    'CoffeeMachine': {'Mug'},
    'Microwave': {'Apple',
                  'AppleSliced',
                  'Bowl',
                  'Bread',
                  'BreadSliced',
                  'Cup',
                  'Egg',
                  'Glassbottle',
                  'Mug',
                  'Plate',
                  'Potato',
                  'PotatoSliced',
                  'Tomato',
                  'TomatoSliced'},
    'StoveBurner': {'Kettle',
                    'Pan',
                    'Pot'},
    'Fridge': {'Apple',
               'AppleSliced',
               'Bowl',
               'Bread',
               'BreadSliced',
               'Cup',
               'Egg',
               'Glassbottle',
               'Lettuce',
               'LettuceSliced',
               'Mug',
               'Pan',
               'Plate',
               'Pot',
               'Potato',
               'PotatoSliced',
               'Tomato',
               'TomatoSliced',
               'WineBottle'},
    'Mug': {'ButterKnife',
            'Fork',
            'Knife',
            'Pen',
            'Pencil',
            'Spoon',
            'KeyChain',
            'Watch'},
    'Plate': {'Apple',
              'AppleSliced',
              'ButterKnife',
              'DishSponge',
              'Egg',
              'Fork',
              'Knife',
              'Ladle',
              'Lettuce',
              'LettuceSliced',
              'Mug',
              'Potato',
              'PotatoSliced',
              'Spatula',
              'Spoon',
              'Tomato',
              'TomatoSliced',
              'AlarmClock',
              'Book',
              'Candle',
              'CD',
              'CellPhone',
              'Cloth',
              'CreditCard',
              'DishSponge',
              'Glassbottle',
              'KeyChain',
              'Mug',
              'PaperTowel',
              'Pen',
              'Pencil',
              'TissueBox',
              'Watch'},
    'Cup': {'ButterKnife',
            'Fork',
            'Spoon'},
    'Sofa': {'BasketBall',
             'Book',
             'Box',
             'CellPhone',
             'Cloth',
             'CreditCard',
             'KeyChain',
             'Laptop',
             'Newspaper',
             'Pillow',
             'RemoteControl'},
    'ArmChair': {'BasketBall',
                 'Book',
                 'Box',
                 'CellPhone',
                 'Cloth',
                 'CreditCard',
                 'KeyChain',
                 'Laptop',
                 'Newspaper',
                 'Pillow',
                 'RemoteControl'},
    'Box': {'AlarmClock',
            'Book',
            'Candle',
            'CD',
            'CellPhone',
            'Cloth',
            'CreditCard',
            'DishSponge',
            'Glassbottle',
            'KeyChain',
            'Mug',
            'PaperTowel',
            'Pen',
            'Pencil',
            'RemoteControl',
            'Statue',
            'TissueBox',
            'Vase',
            'Watch'},
    'Ottoman': {'BasketBall',
                'Book',
                'Box',
                'CellPhone',
                'Cloth',
                'CreditCard',
                'KeyChain',
                'Laptop',
                'Newspaper',
                'Pillow',
                'RemoteControl'},
    'Dresser': {'AlarmClock',
                'BasketBall',
                'Book',
                'Bowl',
                'Box',
                'Candle',
                'CD',
                'CellPhone',
                'Cloth',
                'CreditCard',
                'Cup',
                'Glassbottle',
                'KeyChain',
                'Laptop',
                'Mug',
                'Newspaper',
                'Pen',
                'Pencil',
                'Plate',
                'RemoteControl',
                'SprayBottle',
                'Statue',
                'TennisRacket',
                'TissueBox',
                'ToiletPaper',
                'ToiletPaperRoll',
                'Vase',
                'Watch',
                'WateringCan',
                'WineBottle'},
    'LaundryHamper': {'Cloth'},
    'Desk': {'AlarmClock',
             'BasketBall',
             'Book',
             'Bowl',
             'Box',
             'Candle',
             'CD',
             'CellPhone',
             'Cloth',
             'CreditCard',
             'Cup',
             'Glassbottle',
             'KeyChain',
             'Laptop',
             'Mug',
             'Newspaper',
             'Pen',
             'Pencil',
             'Plate',
             'RemoteControl',
             'SoapBottle',
             'SprayBottle',
             'Statue',
             'TennisRacket',
             'TissueBox',
             'ToiletPaper',
             'ToiletPaperRoll',
             'Vase',
             'Watch',
             'WateringCan',
             'WineBottle'},
    'Bed': {'BaseballBat',
            'BasketBall',
            'Book',
            'CellPhone',
            'Laptop',
            'Newspaper',
            'Pillow',
            'TennisRacket'},
    'Toilet': {'Candle',
               'Cloth',
               'DishSponge',
               'Newspaper',
               'PaperTowel',
               'SoapBar',
               'SoapBottle',
               'SprayBottle',
               'TissueBox',
               'ToiletPaper',
               'ToiletPaperRoll',
               'HandTowel'},
    'ToiletPaperHanger': {'ToiletPaper',
                          'ToiletPaperRoll'},
    'TowelHolder': {'Towel'},
    'HandTowelHolder': {'HandTowel'},
    'Cart': {'Candle',
             'Cloth',
             'DishSponge',
             'Mug',
             'PaperTowel',
             'Plunger',
             'SoapBar',
             'SoapBottle',
             'SprayBottle',
             'Statue',
             'TissueBox',
             'ToiletPaper',
             'ToiletPaperRoll',
             'Vase',
             'HandTowel'},
    'BathtubBasin': {'Cloth',
                     'DishSponge',
                     'SoapBar',
                     'HandTowel'},
    'SinkBasin': {'Apple',
                  'AppleSliced',
                  'Bowl',
                  'ButterKnife',
                  'Cloth',
                  'Cup',
                  'DishSponge',
                  'Egg',
                  'Glassbottle',
                  'Fork',
                  'Kettle',
                  'Knife',
                  'Ladle',
                  'Lettuce',
                  'LettuceSliced',
                  'Mug',
                  'Pan',
                  'Plate',
                  'Pot',
                  'Potato',
                  'PotatoSliced',
                  'SoapBar',
                  'Spatula',
                  'Spoon',
                  'Tomato',
                  'TomatoSliced',
                  'HandTowel'},
    'Cabinet': {'Book',
                'Bowl',
                'Box',
                'Candle',
                'CD',
                'Cloth',
                'Cup',
                'DishSponge',
                'Glassbottle',
                'Kettle',
                'Ladle',
                'Mug',
                'Newspaper',
                'Pan',
                'PepperShaker',
                'Plate',
                'Plunger',
                'Pot',
                'SaltShaker',
                'SoapBar',
                'SoapBottle',
                'SprayBottle',
                'TissueBox',
                'ToiletPaper',
                'ToiletPaperRoll',
                'Vase',
                'WateringCan',
                'WineBottle',
                'HandTowel'},
    'TableTop': {'AlarmClock',
                 'Apple',
                 'AppleSliced',
                 'BaseballBat',
                 'BasketBall',
                 'Book',
                 'Bowl',
                 'Box',
                 'Bread',
                 'BreadSliced',
                 'ButterKnife',
                 'Candle',
                 'CD',
                 'CellPhone',
                 'Cloth',
                 'CreditCard',
                 'Cup',
                 'DishSponge',
                 'Glassbottle',
                 'Egg',
                 'Fork',
                 'Kettle',
                 'KeyChain',
                 'Knife',
                 'Ladle',
                 'Laptop',
                 'Lettuce',
                 'LettuceSliced',
                 'Mug',
                 'Newspaper',
                 'Pan',
                 'PaperTowel',
                 'Pen',
                 'Pencil',
                 'PepperShaker',
                 'Plate',
                 'Pot',
                 'Potato',
                 'PotatoSliced',
                 'RemoteControl',
                 'SaltShaker',
                 'SoapBar',
                 'SoapBottle',
                 'Spatula',
                 'Spoon',
                 'SprayBottle',
                 'Statue',
                 'TennisRacket',
                 'TissueBox',
                 'ToiletPaper',
                 'ToiletPaperRoll',
                 'Tomato',
                 'TomatoSliced',
                 'Vase',
                 'Watch',
                 'WateringCan',
                 'WineBottle',
                 'HandTowel'},
    'CounterTop': {'AlarmClock',
                   'Apple',
                   'AppleSliced',
                   'BaseballBat',
                   'BasketBall',
                   'Book',
                   'Bowl',
                   'Box',
                   'Bread',
                   'BreadSliced',
                   'ButterKnife',
                   'Candle',
                   'CD',
                   'CellPhone',
                   'Cloth',
                   'CreditCard',
                   'Cup',
                   'DishSponge',
                   'Egg',
                   'Glassbottle',
                   'Fork',
                   'Kettle',
                   'KeyChain',
                   'Knife',
                   'Ladle',
                   'Laptop',
                   'Lettuce',
                   'LettuceSliced',
                   'Mug',
                   'Newspaper',
                   'Pan',
                   'PaperTowel',
                   'Pen',
                   'Pencil',
                   'PepperShaker',
                   'Plate',
                   'Pot',
                   'Potato',
                   'PotatoSliced',
                   'RemoteControl',
                   'SaltShaker',
                   'SoapBar',
                   'SoapBottle',
                   'Spatula',
                   'Spoon',
                   'SprayBottle',
                   'Statue',
                   'TennisRacket',
                   'TissueBox',
                   'ToiletPaper',
                   'ToiletPaperRoll',
                   'Tomato',
                   'TomatoSliced',
                   'Vase',
                   'Watch',
                   'WateringCan',
                   'WineBottle',
                   'HandTowel'},
    'Shelf': {'AlarmClock',
              'Book',
              'Bowl',
              'Box',
              'Candle',
              'CD',
              'CellPhone',
              'Cloth',
              'CreditCard',
              'Cup',
              'DishSponge',
              'Glassbottle',
              'Kettle',
              'KeyChain',
              'Mug',
              'Newspaper',
              'PaperTowel',
              'Pen',
              'Pencil',
              'PepperShaker',
              'Plate',
              'Pot',
              'RemoteControl',
              'SaltShaker',
              'SoapBar',
              'SoapBottle',
              'SprayBottle',
              'Statue',
              'TissueBox',
              'ToiletPaper',
              'ToiletPaperRoll',
              'Vase',
              'Watch',
              'WateringCan',
              'WineBottle',
              'HandTowel'},
    'Drawer': {'Book',
               'ButterKnife',
               'Candle',
               'CD',
               'CellPhone',
               'Cloth',
               'CreditCard',
               'DishSponge',
               'Fork',
               'KeyChain',
               'Knife',
               'Ladle',
               'Newspaper',
               'Pen',
               'Pencil',
               'PepperShaker',
               'RemoteControl',
               'SaltShaker',
               'SoapBar',
               'SoapBottle',
               'Spatula',
               'Spoon',
               'SprayBottle',
               'TissueBox',
               'ToiletPaper',
               'ToiletPaperRoll',
               'Watch',
               'WateringCan',
               'HandTowel'},
    'GarbageCan': {'Apple',
                   'AppleSliced',
                   'Bread',
                   'BreadSliced',
                   'CD',
                   'Cloth',
                   'DishSponge',
                   'Egg',
                   'Lettuce',
                   'LettuceSliced',
                   'Newspaper',
                   'PaperTowel',
                   'Pen',
                   'Pencil',
                   'Potato',
                   'PotatoSliced',
                   'SoapBar',
                   'SoapBottle',
                   'SprayBottle',
                   'TissueBox',
                   'ToiletPaper',
                   'ToiletPaperRoll',
                   'Tomato',
                   'TomatoSliced',
                   'WineBottle',
                   'HandTowel'},
    'Safe': {'CD',
             'CellPhone',
             'CreditCard',
             'KeyChain',
             'Statue',
             'Vase',
             'Watch'},
    'TVStand': {'TissueBox'},
    'Toaster': {'BreadSliced'},
}
VAL_RECEPTACLE_OBJECTS['DiningTable'] = VAL_RECEPTACLE_OBJECTS['TableTop']
VAL_RECEPTACLE_OBJECTS['CoffeeTable'] = VAL_RECEPTACLE_OBJECTS['TableTop']
VAL_RECEPTACLE_OBJECTS['SideTable'] = VAL_RECEPTACLE_OBJECTS['TableTop']
del VAL_RECEPTACLE_OBJECTS['TableTop']

NON_RECEPTACLES_SET = (OBJECTS_SET - set(VAL_RECEPTACLE_OBJECTS.keys())) | set(MOVABLE_RECEPTACLES)

VAL_ACTION_OBJECTS = {
    'Heatable': {'Apple',
                 'AppleSliced',
                 'Bread',
                 'BreadSliced',
                 'Cup',
                 'Egg',
                 'Mug',
                 'Plate',
                 'Potato',
                 'PotatoSliced',
                 'Tomato',
                 'TomatoSliced'},
    'Coolable': {'Apple',
                 'AppleSliced',
                 'Bowl',
                 'Bread',
                 'BreadSliced',
                 'Cup',
                 'Egg',
                 'Lettuce',
                 'LettuceSliced',
                 'Mug',
                 'Pan',
                 'Plate',
                 'Pot',
                 'Potato',
                 'PotatoSliced',
                 'Tomato',
                 'TomatoSliced',
                 'WineBottle'},
    'Cleanable': {'Apple',
                  'AppleSliced',
                  'Bowl',
                  'ButterKnife',
                  'Cloth',
                  'Cup',
                  'DishSponge',
                  'Egg',
                  'Fork',
                  'Kettle',
                  'Knife',
                  'Ladle',
                  'Lettuce',
                  'LettuceSliced',
                  'Mug',
                  'Pan',
                  'Plate',
                  'Pot',
                  'Potato',
                  'PotatoSliced',
                  'SoapBar',
                  'Spatula',
                  'Spoon',
                  'Tomato',
                  'TomatoSliced'},
    'Toggleable': {'DeskLamp',
                   'FloorLamp'},
    'Sliceable': {'Apple',
                  'Bread',
                  'Egg',
                  'Lettuce',
                  'Potato',
                  'Tomato'}
}


RECEPTACLES_SB = set(RECEPTACLES) | {'Sink', 'Bathtub'}
OBJECTS_DETECTOR = (set(OBJECTS_WSLICED) - set(RECEPTACLES_SB)) | set(MOVABLE_RECEPTACLES)
OBJECTS_DETECTOR -= {'Blinds', 'Boots', 'Cart', 'Chair', 'Curtains', 'Footstool', 'Mirror', 'LightSwtich', 'Painting', 'Poster', 'ShowerGlass', 'Window'}
STATIC_RECEPTACLES = set(RECEPTACLES_SB) - set(MOVABLE_RECEPTACLES)

OBJECTS_DETECTOR = sorted(list(OBJECTS_DETECTOR))
STATIC_RECEPTACLES = sorted(list(STATIC_RECEPTACLES))
ALL_DETECTOR = sorted(list(set(OBJECTS_DETECTOR) | set(STATIC_RECEPTACLES)))

# object parents
OBJ_PARENTS = {obj: obj for obj in OBJECTS}
OBJ_PARENTS['AppleSliced'] = 'Apple'
OBJ_PARENTS['BreadSliced'] = 'Bread'
OBJ_PARENTS['LettuceSliced'] = 'Lettuce'
OBJ_PARENTS['PotatoSliced'] = 'Potato'
OBJ_PARENTS['TomatoSliced'] = 'Tomato'

# force a different horizon view for objects of (type, location). If the location is None, force this horizon for all
# objects of that type.
FORCED_HORIZON_OBJS = {
    ('FloorLamp', None): 0,
    ('Fridge', 18): 30,
    ('Toilet', None): 15,
}

# openable objects with fixed states for transport.
FORCED_OPEN_STATE_ON_PICKUP = {
    'Laptop': False,
}

# list of openable classes.
OPENABLE_CLASS_LIST = ['Fridge', 'Cabinet', 'Microwave', 'Drawer', 'Safe', 'Box']
OPENABLE_CLASS_SET = set(OPENABLE_CLASS_LIST)

########################################################################################################################