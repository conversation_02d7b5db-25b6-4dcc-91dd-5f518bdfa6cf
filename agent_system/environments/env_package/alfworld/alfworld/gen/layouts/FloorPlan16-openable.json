{"Cabinet|+01.14|+00.39|-01.36": [1.75, -0.5, 180, 30], "Cabinet|+01.14|+02.23|-01.71": [1.0, -0.75, 180, -30], "Cabinet|+01.50|+02.48|-01.71": [2.25, -0.5, 180, -30], "Cabinet|+02.39|+02.48|-01.71": [1.75, -0.5, 180, -30], "Cabinet|+02.47|+00.38|-00.48": [1.5, 0.25, 90, 30], "Cabinet|+02.47|+00.38|-00.49": [1.5, -0.25, 90, 30], "Cabinet|+02.47|+00.38|-01.34": [1.5, -0.75, 90, 30], "Cabinet|+02.47|+00.47|+00.37": [1.5, -0.25, 90, 30], "Cabinet|+02.73|+02.23|-01.71": [2.25, -1.0, 180, -30], "Cabinet|+02.75|+02.22|+00.02": [1.75, -0.5, 90, -30], "Cabinet|+02.75|+02.22|-00.83": [2.0, -0.5, 90, -30], "Cabinet|+02.75|+02.22|-00.84": [1.75, -0.75, 90, -30], "Cabinet|+02.75|+02.22|-01.69": [2.0, -1.0, 90, -30], "Cabinet|-00.36|+00.38|+00.27": [0.5, 1.0, 270, 30], "Cabinet|-00.36|+00.38|+01.71": [0.5, 1.5, 270, 30], "Cabinet|-00.36|+00.38|+02.63": [0.5, 2.0, 270, 30], "Cabinet|-00.66|+02.23|+00.59": [0.0, 0.25, 270, -30], "Cabinet|-00.82|+02.22|+00.62": [0.0, 0.75, 270, -30], "Cabinet|-00.82|+02.22|+01.60": [0.0, 1.25, 270, -30], "Cabinet|-00.82|+02.22|+01.62": [0.0, 1.75, 270, -30], "Cabinet|-00.82|+02.22|+02.61": [0.0, 2.5, 270, -30], "CounterTop|+02.81|+00.99|+00.68": [2.25, -0.5, 90, 30], "CounterTop|-00.81|+01.06|+02.19": [0.0, 2.25, 270, 30], "DiningTable|+02.45|+00.67|+03.98": [2.5, 3.0, 0, 30], "Drawer|+01.35|+00.84|-01.54": [0.75, -1.0, 90, 30], "Drawer|+01.65|+00.83|+01.67": [1.0, 0.75, 0, 30], "Drawer|+01.94|+00.83|+01.35": [1.0, 1.0, 90, 30], "Drawer|+02.23|+00.83|+01.04": [1.25, 0.5, 90, 30], "Drawer|+02.52|+00.83|+00.72": [2.0, -0.25, 0, 30], "Drawer|+02.69|+00.83|+00.16": [1.75, 0.0, 90, 30], "Drawer|+02.69|+00.83|-00.27": [1.75, 0.0, 90, 30], "Drawer|+02.69|+00.83|-00.70": [1.75, -0.75, 90, 30], "Drawer|+02.69|+00.83|-01.13": [1.75, -0.75, 90, 30], "Drawer|-00.64|+00.83|+00.62": [0.5, 0.5, 270, 30], "Drawer|-00.64|+00.83|+01.95": [0.5, 2.25, 270, 30], "Drawer|-00.64|+00.83|+02.42": [0.5, 2.75, 270, 30], "Fridge|-00.91|+00.00|-00.41": [0.0, -0.25, 270, 0], "Microwave|+01.94|+01.75|-01.83": [2.0, -1.0, 180, -30], "Sink|-00.72|+01.02|+01.33|SinkBasin": [0.0, 1.5, 270, 30]}