{"Cabinet|+00.49|+02.06|-01.69": [0.5, -0.75, 180, -30], "Cabinet|+00.83|+00.40|-01.39": [0.25, -0.5, 180, 30], "Cabinet|+00.86|+00.40|+00.67": [0.0, 0.0, 90, 30], "Cabinet|+00.86|+00.40|-00.55": [0.0, -0.25, 90, 30], "Cabinet|+00.86|+00.40|-01.37": [0.0, -0.75, 90, 30], "Cabinet|+01.16|+02.06|-00.34": [0.5, -0.75, 90, -30], "Cabinet|+01.16|+02.06|-01.02": [0.25, -1.0, 90, -30], "Cabinet|-00.19|+02.06|-01.69": [0.25, -1.0, 180, -30], "Cabinet|-00.20|+00.40|-01.39": [0.5, -0.5, 180, 30], "Cabinet|-00.24|+00.40|-01.39": [-0.75, -0.5, 180, 30], "Cabinet|-00.69|+00.75|-01.39": [-1.0, -0.75, 180, 30], "Cabinet|-00.82|+00.40|-01.39": [-0.25, -0.5, 180, 30], "Cabinet|-00.82|+02.06|-01.69": [-0.5, -1.0, 180, -30], "Cabinet|-00.87|+02.01|-01.69": [-1.25, -0.5, 180, 0], "Cabinet|-01.61|+02.01|-01.69": [-1.0, -0.5, 180, 0], "Cabinet|-01.66|+02.06|-01.68": [-2.0, -1.0, 180, -30], "Cabinet|-01.67|+00.40|-01.39": [-1.5, -0.5, 180, 30], "Cabinet|-02.24|+00.40|-01.39": [-1.5, -0.5, 180, 30], "CounterTop|+01.17|+00.95|-00.65": [0.5, -1.0, 90, 30], "CounterTop|+01.50|+01.20|-00.66": [0.5, -1.0, 90, 0], "CounterTop|-01.97|+00.95|-01.71": [-2.0, -1.0, 180, 30], "CounterTop|-02.10|+00.95|+00.29": [-2.0, -0.5, 0, 30], "Drawer|+00.59|+00.75|-01.39": [-0.25, -1.0, 90, 30], "Drawer|+00.86|+00.75|+00.43": [0.25, 0.25, 90, 30], "Drawer|+00.86|+00.75|-00.70": [0.25, -1.0, 90, 30], "Drawer|+00.87|+00.75|-01.14": [0.0, -0.75, 90, 30], "Drawer|-00.38|+00.75|-01.39": [-1.0, -1.0, 90, 30], "Drawer|-01.80|+00.75|-01.39": [-1.25, -1.0, 270, 30], "Drawer|-02.11|+00.75|-01.39": [-1.5, -1.0, 270, 30], "Fridge|+01.42|+00.00|+02.10": [0.5, 2.25, 90, 0], "GarbageCan|+01.38|+00.00|+01.08": [0.25, 1.75, 90, 30], "Microwave|+01.42|+01.15|+00.02": [0.5, 0.0, 90, 30], "Sink|+00.16|+00.82|-01.80|SinkBasin": [-0.25, -1.0, 180, 30]}