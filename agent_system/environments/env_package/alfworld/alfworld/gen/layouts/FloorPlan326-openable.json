{"Bed|-00.63|+00.00|-00.55": [0.75, -0.75, 270, 30], "Bed|-02.46|+00.01|-00.55": [-2.25, -2.5, 0, 30], "DiningTable|+03.16|-00.01|-01.42": [2.0, -1.25, 90, 30], "Drawer|-01.52|+00.15|+00.36": [-1.5, -0.75, 0, 30], "Drawer|-01.52|+00.42|+00.36": [-1.5, -0.5, 0, 30], "Shelf|+00.92|+01.13|-01.62": [0.25, -1.5, 90, 30], "Shelf|+00.92|+01.13|-02.16": [0.25, -2.25, 90, 30], "Shelf|+00.92|+01.13|-02.73": [0.25, -2.5, 90, 30], "Shelf|+00.92|+01.41|-01.62": [0.25, -1.5, 90, 0], "Shelf|+00.92|+01.41|-02.16": [0.25, -2.25, 90, 0], "Shelf|+00.92|+01.41|-02.73": [0.25, -2.5, 90, 0], "Shelf|+03.04|+00.53|-00.56": [2.25, -0.25, 90, 30], "Shelf|+03.04|+00.53|-02.26": [2.25, -2.0, 90, 30], "Shelf|-02.95|+01.13|-01.62": [-2.0, -2.0, 270, 0], "Shelf|-02.95|+01.13|-02.19": [-2.25, -2.25, 270, 30], "Shelf|-02.95|+01.13|-02.74": [-2.25, -2.5, 270, 30], "Shelf|-02.95|+01.41|-01.62": [-2.25, -2.0, 270, 0], "Shelf|-02.95|+01.41|-02.19": [-2.25, -2.25, 270, 0], "Shelf|-02.95|+01.41|-02.74": [-2.25, -2.5, 270, 0], "SideTable|-01.52|+00.42|+00.36": [-1.5, -0.25, 0, 30]}